{"name": "@srigi/mcp-google-images-search", "description": "MCP server for searching images with Google", "license": "MIT", "version": "0.2.1", "bugs": {"url": "https://github.com/srigi/mcp-google-images-search/issues"}, "homepage": "https://github.com/srigi/mcp-google-images-search#readme", "repository": {"type": "git", "url": "git+https://github.com/srigi/mcp-google-images-search.git"}, "keywords": ["google", "image", "images", "modelcontextprotocol", "mcp", "search"], "bin": {"mcp-google-images-search": "dist/cli.js"}, "files": ["dist/cli.js", "logs", "src"], "scripts": {"build": "ncc build src/index.ts --minify --out dist/ && echo '#!/usr/bin/env node' | cat - dist/index.js > dist/cli.js && chmod +x dist/cli.js", "build:tsc": "tsc && tsc-alias", "dev": "pnpm build:tsc && chokidar 'src/**/*.ts' -c 'tsc && tsc-alias'", "dev:inspector": "pnpx @modelcontextprotocol/inspector", "lint": "eslint .", "lint:fix": "eslint . --fix", "tsc": "tsc -noEmit", "test": "vitest run", "start": "node dist/cli.js"}, "dependencies": {"@modelcontextprotocol/sdk": "~1.17", "winston": "~3.17", "zod": "~3.25"}, "devDependencies": {"@eslint/js": "~9.32", "@types/node": "^24", "@vercel/ncc": "~0.38", "chokidar-cli": "~3.0", "eslint": "~9.32", "eslint-config-prettier": "~10.1", "eslint-plugin-prettier": "~5.5", "prettier": "~3.6", "tsc-alias": "~1.8", "typescript": "~5.8", "typescript-eslint": "~8.38", "vitest": "~3.2"}, "packageManager": "pnpm@10.14.0-0+sha512.2cd47a0cbf5f1d1de7693a88307a0ede5be94e0d3b34853d800ee775efbea0650cb562b77605ec80bc8d925f5cd27c4dfe8bb04d3a0b76090784c664450d32d6"}